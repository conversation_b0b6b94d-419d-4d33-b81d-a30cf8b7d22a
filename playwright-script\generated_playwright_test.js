const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const { chromium } = require('playwright');

// Helper function to validate step completion
async function validateStepCompletion(page, stepName, validationCode = null) {
    try {
        if (validationCode) {
            // Execute custom validation code with retry logic
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts) {
                try {
                    const result = await page.evaluate(validationCode);
                    if (result) {
                        console.log(`✅ Validation passed for: ${stepName}`);
                        return true;
                    }

                    // Wait a bit before retrying
                    if (attempts < maxAttempts - 1) {
                        await page.waitForTimeout(1000);
                        attempts++;
                        console.log(`🔄 Retrying validation for: ${stepName} (attempt ${attempts + 1}/${maxAttempts})`);
                    } else {
                        break;
                    }
                } catch (evalError) {
                    console.log(`⚠️  Validation evaluation error for ${stepName}:`, evalError.message);
                    if (attempts < maxAttempts - 1) {
                        await page.waitForTimeout(1000);
                        attempts++;
                    } else {
                        break;
                    }
                }
            }

            console.log(`❌ Validation failed for: ${stepName} after ${maxAttempts} attempts`);
            return false;
        } else {
            // Default validation - check if page is responsive
            await page.evaluate(() => document.readyState);
            console.log(`✅ Basic validation passed for: ${stepName}`);
            return true;
        }
    } catch (error) {
        console.log(`❌ Validation error for ${stepName}:`, error.message);
        return false;
    }
}

// Helper function to wait for page stability
async function waitForPageStability(page, timeout = 5000) {
    try {
        // First, wait for DOM content to be loaded
        await page.waitForLoadState('domcontentloaded', { timeout: timeout / 2 });

        // Then wait for network to be idle (shorter timeout)
        await page.waitForLoadState('networkidle', { timeout: timeout / 2 });

        // Wait for any loading indicators to disappear
        try {
            await page.waitForFunction(() => {
                const loadingElements = document.querySelectorAll('.loading, .spinner, [data-loading="true"], .loader');
                return loadingElements.length === 0;
            }, { timeout: 2000 });
        } catch (loadingTimeout) {
            // Continue if loading indicators don't disappear
        }

        // Additional wait for any dynamic content
        await page.waitForTimeout(1000);
        console.log('📄 Page stability confirmed');
    } catch (error) {
        console.log('⚠️  Page stability timeout, continuing...');
        // Even if stability times out, wait a minimum amount
        await page.waitForTimeout(500);
    }
}

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();

    try {
        console.log('🚀 Starting Playwright test execution...');

        // Wait for initial page load
        await waitForPageStability(page);

        
        // 
        console.log("🔄 Navigated to https://www.mindler.com/partner/login");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.goto('https://www.mindler.com/partner/login', { timeout: 5000 });
            console.log("✅ Navigate to login page executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Navigate to login page", () => {
                // Check for login success indicators
                const loginSuccessIndicators = [
                    // Common success elements
                    '[data-testid*="dashboard"]', '[data-testid*="profile"]', '[data-testid*="user"]',
                    '.dashboard', '.profile', '.user-menu', '.logout', '.sign-out',
                    // Text-based indicators
                    '*[text()*="welcome" i]', '*[text()*="dashboard" i]', '*[text()*="logout" i]',
                    // Navigation indicators
                    'nav[role="navigation"]', '[role="banner"]'
                ];

                // Check if URL changed to a logged-in area
                const urlIndicatesLogin = window.location.href.includes('dashboard') ||
                                        window.location.href.includes('profile') ||
                                        window.location.href.includes('home') ||
                                        !window.location.href.includes('login');

                // Check for logout button or user menu (indicates successful login)
                const hasLogoutButton = document.querySelector('button[type="button"]') &&
                                      (document.body.textContent.toLowerCase().includes('logout') ||
                                       document.body.textContent.toLowerCase().includes('sign out'));

                // Check for any success indicators
                const hasSuccessIndicators = loginSuccessIndicators.some(selector => {
                    try {
                        return document.querySelector(selector) !== null;
                    } catch (e) {
                        return false;
                    }
                });

                return urlIndicatesLogin || hasLogoutButton || hasSuccessIndicators;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Navigate to login page failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Entered email and password, clicked Log In button");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.fill('input[formcontrolname="email"]', '<EMAIL>', { timeout: 4000 });
await page.fill('input[formcontrolname="password"]', '123456', { timeout: 4000 });
await page.click('button[type="submit"]', { timeout: 4000 });
            console.log("✅ Input email and password and click Log In executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Input email and password and click Log In", () => {
                // Check for login success indicators
                const loginSuccessIndicators = [
                    // Common success elements
                    '[data-testid*="dashboard"]', '[data-testid*="profile"]', '[data-testid*="user"]',
                    '.dashboard', '.profile', '.user-menu', '.logout', '.sign-out',
                    // Text-based indicators
                    '*[text()*="welcome" i]', '*[text()*="dashboard" i]', '*[text()*="logout" i]',
                    // Navigation indicators
                    'nav[role="navigation"]', '[role="banner"]'
                ];

                // Check if URL changed to a logged-in area
                const urlIndicatesLogin = window.location.href.includes('dashboard') ||
                                        window.location.href.includes('profile') ||
                                        window.location.href.includes('home') ||
                                        !window.location.href.includes('login');

                // Check for logout button or user menu (indicates successful login)
                const hasLogoutButton = document.querySelector('button[type="button"]') &&
                                      (document.body.textContent.toLowerCase().includes('logout') ||
                                       document.body.textContent.toLowerCase().includes('sign out'));

                // Check for any success indicators
                const hasSuccessIndicators = loginSuccessIndicators.some(selector => {
                    try {
                        return document.querySelector(selector) !== null;
                    } catch (e) {
                        return false;
                    }
                });

                return urlIndicatesLogin || hasLogoutButton || hasSuccessIndicators;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Input email and password and click Log In failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Students\' tab");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(5)', { timeout: 4000 });
            console.log("✅ Click on the \'Students\' tab executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Students\' tab", () => {
                const inputs = document.querySelectorAll('input, textarea, select');
                const filledInputs = Array.from(inputs).filter(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        return input.checked;
                    }
                    return input.value && input.value.trim().length > 0;
                });

                // Also check if any input has focus (indicating interaction)
                const hasActiveFocus = document.activeElement &&
                                     (document.activeElement.tagName === 'INPUT' ||
                                      document.activeElement.tagName === 'TEXTAREA');

                return filledInputs.length > 0 || hasActiveFocus;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Students\' tab failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Log Out\' button");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(11)', { timeout: 4000 });
            console.log("✅ Click on the \'Log Out\' button executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Log Out\' button", () => {
                // Check for logout success indicators
                const logoutSuccessIndicators = [
                    // Login form elements
                    'input[type="email"]', 'input[type="password"]', 'input[name*="email"]',
                    'input[name*="password"]', 'input[name*="username"]',
                    // Login buttons
                    'button[type="submit"]', 'input[type="submit"]',
                    // Login page elements
                    '.login-form', '.signin-form', '#login', '#signin'
                ];

                // Check if URL changed to login page
                const urlIndicatesLogout = window.location.href.includes('login') ||
                                         window.location.href.includes('signin') ||
                                         window.location.href.includes('auth');

                // Check for login form presence
                const hasLoginForm = logoutSuccessIndicators.some(selector => {
                    try {
                        return document.querySelector(selector) !== null;
                    } catch (e) {
                        return false;
                    }
                });

                // Check if logout/user menu disappeared
                const noUserMenu = !document.body.textContent.toLowerCase().includes('logout') &&
                                 !document.body.textContent.toLowerCase().includes('sign out');

                return urlIndicatesLogout || hasLoginForm || noUserMenu;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Log Out\' button failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Re-entered email and password, clicked Log In button");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.fill('input[formcontrolname="email"]', '<EMAIL>', { timeout: 4000 });
await page.fill('input[formcontrolname="password"]', '123456', { timeout: 4000 });
await page.click('button[type="submit"]', { timeout: 4000 });
            console.log("✅ Re-attempt login with email and password and click Log In executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Re-attempt login with email and password and click Log In", () => {
                // Check for login success indicators
                const loginSuccessIndicators = [
                    // Common success elements
                    '[data-testid*="dashboard"]', '[data-testid*="profile"]', '[data-testid*="user"]',
                    '.dashboard', '.profile', '.user-menu', '.logout', '.sign-out',
                    // Text-based indicators
                    '*[text()*="welcome" i]', '*[text()*="dashboard" i]', '*[text()*="logout" i]',
                    // Navigation indicators
                    'nav[role="navigation"]', '[role="banner"]'
                ];

                // Check if URL changed to a logged-in area
                const urlIndicatesLogin = window.location.href.includes('dashboard') ||
                                        window.location.href.includes('profile') ||
                                        window.location.href.includes('home') ||
                                        !window.location.href.includes('login');

                // Check for logout button or user menu (indicates successful login)
                const hasLogoutButton = document.querySelector('button[type="button"]') &&
                                      (document.body.textContent.toLowerCase().includes('logout') ||
                                       document.body.textContent.toLowerCase().includes('sign out'));

                // Check for any success indicators
                const hasSuccessIndicators = loginSuccessIndicators.some(selector => {
                    try {
                        return document.querySelector(selector) !== null;
                    } catch (e) {
                        return false;
                    }
                });

                return urlIndicatesLogin || hasLogoutButton || hasSuccessIndicators;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Re-attempt login with email and password and click Log In failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Students\' tab again");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(5)', { timeout: 4000 });
            console.log("✅ Click on the \'Students\' tab again executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Students\' tab again", () => {
                const inputs = document.querySelectorAll('input, textarea, select');
                const filledInputs = Array.from(inputs).filter(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        return input.checked;
                    }
                    return input.value && input.value.trim().length > 0;
                });

                // Also check if any input has focus (indicating interaction)
                const hasActiveFocus = document.activeElement &&
                                     (document.activeElement.tagName === 'INPUT' ||
                                      document.activeElement.tagName === 'TEXTAREA');

                return filledInputs.length > 0 || hasActiveFocus;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Students\' tab again failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Log Out\' button again");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(11)', { timeout: 4000 });
            console.log("✅ Click on the \'Log Out\' button again executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Log Out\' button again", () => {
                // Check for logout success indicators
                const logoutSuccessIndicators = [
                    // Login form elements
                    'input[type="email"]', 'input[type="password"]', 'input[name*="email"]',
                    'input[name*="password"]', 'input[name*="username"]',
                    // Login buttons
                    'button[type="submit"]', 'input[type="submit"]',
                    // Login page elements
                    '.login-form', '.signin-form', '#login', '#signin'
                ];

                // Check if URL changed to login page
                const urlIndicatesLogout = window.location.href.includes('login') ||
                                         window.location.href.includes('signin') ||
                                         window.location.href.includes('auth');

                // Check for login form presence
                const hasLoginForm = logoutSuccessIndicators.some(selector => {
                    try {
                        return document.querySelector(selector) !== null;
                    } catch (e) {
                        return false;
                    }
                });

                // Check if logout/user menu disappeared
                const noUserMenu = !document.body.textContent.toLowerCase().includes('logout') &&
                                 !document.body.textContent.toLowerCase().includes('sign out');

                return urlIndicatesLogout || hasLoginForm || noUserMenu;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Log Out\' button again failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Successfully logged in, navigated to \'Students\' tab, and logged out; task completed");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            // Task complete - no action needed
            console.log("✅ Complete task with successful logout executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Complete task with successful logout", () => {
                // Check for logout success indicators
                const logoutSuccessIndicators = [
                    // Login form elements
                    'input[type="email"]', 'input[type="password"]', 'input[name*="email"]',
                    'input[name*="password"]', 'input[name*="username"]',
                    // Login buttons
                    'button[type="submit"]', 'input[type="submit"]',
                    // Login page elements
                    '.login-form', '.signin-form', '#login', '#signin'
                ];

                // Check if URL changed to login page
                const urlIndicatesLogout = window.location.href.includes('login') ||
                                         window.location.href.includes('signin') ||
                                         window.location.href.includes('auth');

                // Check for login form presence
                const hasLoginForm = logoutSuccessIndicators.some(selector => {
                    try {
                        return document.querySelector(selector) !== null;
                    } catch (e) {
                        return false;
                    }
                });

                // Check if logout/user menu disappeared
                const noUserMenu = !document.body.textContent.toLowerCase().includes('logout') &&
                                 !document.body.textContent.toLowerCase().includes('sign out');

                return urlIndicatesLogout || hasLoginForm || noUserMenu;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Complete task with successful logout failed:", error.message);
            throw error;
        }

        console.log('✅ Test completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();
    }
})();