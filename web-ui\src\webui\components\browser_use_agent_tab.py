import asyncio
import base64
import json
import logging
import os
import uuid
import time
from typing import Any, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Optional

import gradio as gr
import aiohttp
import openai

# from browser_use.agent.service import Agent
from browser_use.agent.views import (
    AgentH<PERSON>oryList,
    AgentOutput,
)
from browser_use.browser.browser import BrowserConfig
from browser_use.browser.context import <PERSON><PERSON><PERSON><PERSON><PERSON>x<PERSON>, BrowserContextConfig
from browser_use.browser.views import BrowserState
from gradio.components import Component
from langchain_core.language_models.chat_models import BaseChatModel

from src.agent.browser_use.browser_use_agent import BrowserUseAgent
from src.browser.custom_browser import CustomBrowser
from src.controller.custom_controller import CustomController
from src.utils import llm_provider
from src.utils.qa_system_prompt import get_qa_system_prompt
from src.webui.webui_manager import WebuiManager

"""
Browser Use Agent Tab for the WebUI.

This module provides the UI components and functionality for running browser automation tasks
using the BrowserUseAgent. It includes chat interface, browser view, and task management.

QA Summary Generation:
- Optional DRCODE_API_URL for saving reports to drCode backend (no API key required)
- Set DEFAULT_QA_MODE=true to enable QA testing mode with AI summaries
"""

logger = logging.getLogger(__name__)


# --- Helper Functions --- (Defined at module level)


async def _initialize_llm(
        provider: Optional[str],
        model_name: Optional[str],
        temperature: float,
        base_url: Optional[str],
        api_key: Optional[str],
        num_ctx: Optional[int] = None,
) -> Optional[BaseChatModel]:
    """Initializes the LLM based on settings. Returns None if provider/model is missing."""
    if not provider or not model_name:
        logger.info("LLM Provider or Model Name not specified, LLM will be None.")
        return None
    try:
        # Use your actual LLM provider logic here
        logger.info(
            f"Initializing LLM: Provider={provider}, Model={model_name}, Temp={temperature}"
        )
        # Example using a placeholder function
        llm = llm_provider.get_llm_model(
            provider=provider,
            model_name=model_name,
            temperature=temperature,
            base_url=base_url or None,
            api_key=api_key or None,
            # Add other relevant params like num_ctx for ollama
            num_ctx=num_ctx if provider == "ollama" else None,
        )
        return llm
    except Exception as e:
        logger.error(f"Failed to initialize LLM: {e}", exc_info=True)
        gr.Warning(
            f"Failed to initialize LLM '{model_name}' for provider '{provider}'. Please check settings. Error: {e}"
        )
        return None


def _get_config_value(
        webui_manager: WebuiManager,
        comp_dict: Dict[gr.components.Component, Any],
        comp_id_suffix: str,
        default: Any = None,
) -> Any:
    """Safely get value from component dictionary using its ID suffix relative to the tab."""
    # Assumes component ID format is "tab_name.comp_name"
    tab_name = "browser_use_agent"  # Hardcode or derive if needed
    comp_id = f"{tab_name}.{comp_id_suffix}"
    # Need to find the component object first using the ID from the manager
    try:
        comp = webui_manager.get_component_by_id(comp_id)
        return comp_dict.get(comp, default)
    except KeyError:
        # Try accessing settings tabs as well
        for prefix in ["agent_settings", "browser_settings"]:
            try:
                comp_id = f"{prefix}.{comp_id_suffix}"
                comp = webui_manager.get_component_by_id(comp_id)
                return comp_dict.get(comp, default)
            except KeyError:
                continue
        logger.warning(
            f"Component with suffix '{comp_id_suffix}' not found in manager for value lookup."
        )
        return default


def _format_agent_output(model_output: AgentOutput) -> str:
    """Formats AgentOutput for display in the chatbot using JSON."""
    content = ""
    if model_output:
        try:
            # Directly use model_dump if actions and current_state are Pydantic models
            action_dump = [
                action.model_dump(exclude_none=True) for action in model_output.action
            ]

            state_dump = model_output.current_state.model_dump(exclude_none=True)
            model_output_dump = {
                "current_state": state_dump,
                "action": action_dump,
            }
            # Dump to JSON string with indentation
            json_string = json.dumps(model_output_dump, indent=4, ensure_ascii=False)
            # Wrap in <pre><code> for proper display in HTML
            content = f"<pre><code class='language-json'>{json_string}</code></pre>"

        except AttributeError as ae:
            logger.error(
                f"AttributeError during model dump: {ae}. Check if 'action' or 'current_state' or their items support 'model_dump'."
            )
            content = f"<pre><code>Error: Could not format agent output (AttributeError: {ae}).\nRaw output: {str(model_output)}</code></pre>"
        except Exception as e:
            logger.error(f"Error formatting agent output: {e}", exc_info=True)
            # Fallback to simple string representation on error
            content = f"<pre><code>Error formatting agent output.\nRaw output:\n{str(model_output)}</code></pre>"

    return content.strip()


# --- Add formatting function for user-friendly display ---
def format_agent_interaction(message):
    try:

        print("Message: ", message)
        # Try to parse as JSON if it's a string
        if isinstance(message, str):
            data = json.loads(message)
        elif isinstance(message, dict):
            data = message
        else:
            return str(message)
        # If it looks like an agent step/state
        if 'current_state' in data or 'action' in data:
            state = data.get('current_state', {})
            actions = data.get('action', [])
            lines = []
            if 'evaluation_previous_goal' in state:
                lines.append(f"**Current Status:** {state['evaluation_previous_goal']}")
            if 'memory' in state:
                lines.append(f"**Memory:** {state['memory']}")
            if 'next_goal' in state:
                lines.append(f"**Next Goal:** {state['next_goal']}")
            if actions:
                lines.append("**Current Action:**")
                for act in actions:
                    if 'input_text' in act:
                        lines.append(f"- Entering email: `{act['input_text']['text']}`")
                    # Add more action types as needed
            return '\n\n'.join(lines)
        # Otherwise, just pretty-print the JSON
        return f'```json\n{json.dumps(data, indent=2)}\n```'
    except Exception:
        return str(message)


# --- Updated Callback Implementation ---


async def _handle_new_step(
        webui_manager: WebuiManager, state: BrowserState, output: AgentOutput, step_num: int
):
    """Callback for each step taken by the agent, including screenshot display."""

    # Use the correct chat history attribute name from the user's code
    if not hasattr(webui_manager, "bu_chat_history"):
        logger.error(
            "Attribute 'bu_chat_history' not found in webui_manager! Cannot add chat message."
        )
        # Initialize it maybe? Or raise an error? For now, log and potentially skip chat update.
        webui_manager.bu_chat_history = []  # Initialize if missing (consider if this is the right place)
        # return # Or stop if this is critical
    step_num -= 1
    logger.info(f"Step {step_num} completed.")

    # --- Screenshot Handling ---
    screenshot_html = ""
    # Ensure state.screenshot exists and is not empty before proceeding
    # Use getattr for safer access
    screenshot_data = getattr(state, "screenshot", None)
    if screenshot_data:
        try:
            # Basic validation: check if it looks like base64
            if (
                    isinstance(screenshot_data, str) and len(screenshot_data) > 100
            ):  # Arbitrary length check
                # *** UPDATED STYLE: Removed centering, adjusted width ***
                img_tag = f'<img src="data:image/jpeg;base64,{screenshot_data}" alt="Step {step_num} Screenshot" style="max-width: 800px; max-height: 600px; object-fit:contain;" />'
                screenshot_html = (
                        img_tag + "<br/>"
                )  # Use <br/> for line break after inline-block image
            else:
                logger.warning(
                    f"Screenshot for step {step_num} seems invalid (type: {type(screenshot_data)}, len: {len(screenshot_data) if isinstance(screenshot_data, str) else 'N/A'})."
                )
                screenshot_html = "**[Invalid screenshot data]**<br/>"

        except Exception as e:
            logger.error(
                f"Error processing or formatting screenshot for step {step_num}: {e}",
                exc_info=True,
            )
            screenshot_html = "**[Error displaying screenshot]**<br/>"
    else:
        logger.debug(f"No screenshot available for step {step_num}.")

    # --- Format Agent Output ---
    formatted_output = _format_agent_output(output)  # Use the updated function

    # --- Combine and Append to Chat ---
    step_header = f"--- **Step {step_num}** ---"
    # Combine header, image (with line break), and JSON block
    final_content = step_header + "<br/>" + screenshot_html + formatted_output

    chat_message = {
        "role": "assistant",
        "content": final_content.strip(),  # Remove leading/trailing whitespace
    }

    # Append to the correct chat history list
    webui_manager.bu_chat_history.append(chat_message)

    await asyncio.sleep(0.05)


def _handle_done(webui_manager: WebuiManager, history: AgentHistoryList):
    """Callback when the agent finishes the task (success or failure)."""
    logger.info(
        f"Agent task finished. Duration: {history.total_duration_seconds():.2f}s, Tokens: {history.total_input_tokens()}"
    )

    # Check if QA mode is enabled to format the result appropriately
    qa_mode = os.getenv("DEFAULT_QA_MODE", "true").lower() == "true"

    if qa_mode:
        # First, show loading message
        loading_message = "## 🧪 QA TEST EXECUTION COMPLETED\n\n"
        loading_message += "📝 **Loading summary, please wait...**\n\n"
        loading_message += f"**Execution Summary:**\n"
        loading_message += f"- Duration: {history.total_duration_seconds():.2f} seconds\n"
        loading_message += f"- Total Input Tokens: {history.total_input_tokens()}\n"

        errors = history.errors()
        if errors and any(errors):
            loading_message += f"- Status: ⚠️ Completed with errors\n"
        else:
            loading_message += f"- Status: ✅ Successfully completed\n"

        loading_message += "\n---\n\n"
        loading_message += "🔄 Generating detailed QA report using AI analysis..."

        webui_manager.bu_chat_history.append(
            {"role": "assistant", "content": loading_message}
        )

        # Set flag to indicate summary processing is needed
        webui_manager.bu_summary_processing_needed = True
        webui_manager.bu_summary_history = history

    else:
        # Standard format for non-QA mode (unchanged)
        final_result = history.final_result()
        final_summary = "**Task Completed**\n"
        final_summary += f"- Duration: {history.total_duration_seconds():.2f} seconds\n"
        final_summary += f"- Total Input Tokens: {history.total_input_tokens()}\n"

        if final_result:
            final_summary += f"- Final Result: {final_result}\n"

        errors = history.errors()
        if errors and any(errors):
            final_summary += f"- **Errors:**\n```\n{errors}\n```\n"
        else:
            final_summary += "- Status: Success\n"

        webui_manager.bu_chat_history.append(
            {"role": "assistant", "content": final_summary}
        )


async def _process_qa_summary_async(webui_manager: WebuiManager, history: AgentHistoryList):
    """Async function to process QA summary with OpenAI and update UI."""
    try:
        # Small delay to ensure history file is written
        await asyncio.sleep(1)

        # Get session data from history file
        history_file_path = None
        if hasattr(webui_manager, 'bu_agent_task_id') and webui_manager.bu_agent_task_id:
            save_agent_history_path = "./tmp/agent_history"  # Default path
            history_file_path = os.path.join(
                save_agent_history_path,
                webui_manager.bu_agent_task_id,
                f"{webui_manager.bu_agent_task_id}.json"
            )

        # Load session data from JSON file with retry logic
        session_data = None
        max_retries = 3
        for attempt in range(max_retries):
            if history_file_path and os.path.exists(history_file_path):
                try:
                    with open(history_file_path, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    break
                except (json.JSONDecodeError, IOError) as e:
                    logger.warning(f"Attempt {attempt + 1} failed to read history file: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2)  # Wait before retry
                    else:
                        logger.error(f"Failed to read history file after {max_retries} attempts")
            else:
                logger.warning(f"History file not found: {history_file_path}")
                break

        # Extract required fields for OpenAI
        openai_data = _extract_openai_fields(session_data, history)

        # Get user prompt for context
        user_prompt = _get_user_prompt_from_history(webui_manager)

        # Generate summary using OpenAI
        summary, summary_token_usage = await _generate_openai_summary(openai_data, user_prompt)

        # Update UI with the generated summary
        _update_ui_with_summary(webui_manager, summary, history)

        # Get session start time (default to current time if not set)
        session_start_time = getattr(webui_manager, 'bu_session_start_time', time.time())

        # Save to drCode API
        await _save_to_drcode_api(webui_manager, summary, history, openai_data, summary_token_usage, session_start_time)

        logger.info(f"QA summary processing completed. Token usage: {summary_token_usage}")

    except Exception as e:
        logger.error(f"Error processing QA summary: {e}", exc_info=True)
        # Update UI with error message
        error_summary = "## 🧪 QA TEST EXECUTION COMPLETED\n\n"
        error_summary += f"❌ **Error generating AI summary:** {str(e)}\n\n"
        error_summary += "**Execution Summary:**\n"
        error_summary += f"- Duration: {history.total_duration_seconds():.2f} seconds\n"
        error_summary += f"- Total Input Tokens: {history.total_input_tokens()}\n"

        errors = history.errors()
        if errors and any(errors):
            error_summary += f"- Status: ⚠️ Completed with errors\n"
        else:
            error_summary += f"- Status: ✅ Successfully completed\n"

        # Replace the loading message with error message
        if webui_manager.bu_chat_history and webui_manager.bu_chat_history[-1]["role"] == "assistant":
            webui_manager.bu_chat_history[-1]["content"] = error_summary


def _extract_openai_fields(session_data: dict, history: AgentHistoryList) -> dict:
    """Extract model_output, result, and state.url fields from session data."""
    extracted_data = {
        "model_outputs": [],
        "results": [],
        "urls": []
    }

    if session_data and "history" in session_data:
        for entry in session_data["history"]:
            # Extract model_output
            if "model_output" in entry and entry["model_output"]:
                extracted_data["model_outputs"].append(entry["model_output"])

            # Extract result
            if "result" in entry and entry["result"]:
                extracted_data["results"].append(entry["result"])

            # Extract state.url
            if "state" in entry and entry["state"] and "url" in entry["state"]:
                url = entry["state"]["url"]
                if url and url not in extracted_data["urls"]:
                    extracted_data["urls"].append(url)

    return extracted_data


def _get_user_prompt_from_history(webui_manager: WebuiManager) -> str:
    """Extract the user prompt from chat history."""
    if hasattr(webui_manager, 'bu_chat_history') and webui_manager.bu_chat_history:
        for message in webui_manager.bu_chat_history:
            if message.get("role") == "user":
                return message.get("content", "")
    return "No user prompt found"


async def _generate_openai_summary(openai_data: dict, user_prompt: str) -> tuple[str, dict]:
    """Generate summary using OpenAI API."""
    import openai

    # Get OpenAI API key from environment
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment variables")

    client = openai.AsyncOpenAI(api_key=api_key)

    # Prepare the prompt for OpenAI
    system_prompt = """You are a QA UI Testing Agent analyzer. Generate a comprehensive, human-readable QA testing report in markdown format based on the provided session data.

Your report should include:
1. **Test Overview** - What was being tested
2. **Test Execution Summary** - Key actions taken
3. **Results Analysis** - What was found/achieved
4. **Issues Identified** - Any problems or errors
5. **Recommendations** - Suggestions for improvement

Format the response as clean markdown with proper headers, bullet points, and sections."""

    user_message = f"""Please analyze this QA testing session and generate a detailed report:

**Original User Request:** {user_prompt}

**Session Data:**
- Model Outputs: {json.dumps(openai_data['model_outputs'], indent=2)}
- Results: {json.dumps(openai_data['results'], indent=2)}
- URLs Visited: {openai_data['urls']}

Generate a comprehensive QA testing report in markdown format."""

    try:
        response = await client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            temperature=0.3
        )

        summary = response.choices[0].message.content
        token_usage = {
            "prompt_tokens": response.usage.prompt_tokens,
            "completion_tokens": response.usage.completion_tokens,
            "total_tokens": response.usage.total_tokens
        }

        return summary, token_usage

    except Exception as e:
        logger.error(f"OpenAI API error: {e}")
        raise


def _update_ui_with_summary(webui_manager: WebuiManager, summary: str, history: AgentHistoryList):
    """Update the UI with the generated summary."""
    final_summary = "## 🧪 QA TEST EXECUTION COMPLETED\n\n"
    final_summary += f"**Execution Summary:**\n"
    final_summary += f"- Duration: {history.total_duration_seconds():.2f} seconds\n"
    final_summary += f"- Total Input Tokens: {history.total_input_tokens()}\n"

    errors = history.errors()
    if errors and any(errors):
        final_summary += f"- Status: ⚠️ Completed with errors\n"
    else:
        final_summary += f"- Status: ✅ Successfully completed\n"

    final_summary += "\n---\n\n"
    final_summary += "## 📋 AI-GENERATED QA REPORT\n\n"
    final_summary += summary

    if errors and any(errors):
        final_summary += f"\n\n## ⚠️ EXECUTION ERRORS\n\n```\n{errors}\n```"

    # Replace the loading message with the final summary
    if webui_manager.bu_chat_history and webui_manager.bu_chat_history[-1]["role"] == "assistant":
        webui_manager.bu_chat_history[-1]["content"] = final_summary


async def _generate_playwright_steps_json(session_data: dict) -> tuple[dict, dict]:
    """
    Generate Playwright steps JSON from session data using OpenAI.

    Args:
        session_data: The cleaned JSON data from QA agent

    Returns:
        Tuple of (playwright_steps_json, token_usage)
    """
    try:
        # Get OpenAI API key
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if not openai_api_key:
            logger.warning("OpenAI API key not found, skipping Playwright generation")
            return None, {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}

        import openai
        client = openai.AsyncOpenAI(api_key=openai_api_key)

        # Remove screenshots from session data
        cleaned_data = _remove_screenshots_from_json(session_data)

        # Create prompt for Playwright steps generation
        prompt = _create_playwright_prompt(cleaned_data)

        response = await client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert Playwright automation engineer. Convert QA testing data into structured Playwright steps in JSON format."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0.1
        )

        steps_json_str = response.choices[0].message.content.strip()

        # Extract JSON from response if it's wrapped in markdown
        if "```json" in steps_json_str:
            start = steps_json_str.find("```json") + 7
            end = steps_json_str.find("```", start)
            steps_json_str = steps_json_str[start:end].strip()
        elif "```" in steps_json_str:
            start = steps_json_str.find("```") + 3
            end = steps_json_str.find("```", start)
            steps_json_str = steps_json_str[start:end].strip()

        steps_json = json.loads(steps_json_str)

        token_usage = {
            "prompt_tokens": response.usage.prompt_tokens,
            "completion_tokens": response.usage.completion_tokens,
            "total_tokens": response.usage.total_tokens
        }

        logger.info("Successfully generated Playwright steps JSON using OpenAI")
        return steps_json, token_usage

    except Exception as e:
        logger.error(f"Failed to generate Playwright steps JSON: {e}")
        return None, {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}


def _remove_screenshots_from_json(json_data: Any) -> Any:
    """
    Remove screenshot fields from the JSON data recursively.

    Args:
        json_data: The JSON data to process

    Returns:
        JSON data with screenshot fields removed
    """
    if isinstance(json_data, dict):
        cleaned_data = {}
        for key, value in json_data.items():
            if key.lower() in ['screenshot', 'screenshots']:
                continue  # Skip screenshot fields
            else:
                cleaned_data[key] = _remove_screenshots_from_json(value)
        return cleaned_data
    elif isinstance(json_data, list):
        return [_remove_screenshots_from_json(item) for item in json_data]
    else:
        return json_data


def _create_playwright_prompt(cleaned_json: dict) -> str:
    """
    Create a prompt for OpenAI to generate structured Playwright steps in JSON format.

    Args:
        cleaned_json: The cleaned JSON data

    Returns:
        Formatted prompt string
    """
    json_str = json.dumps(cleaned_json, indent=2)

    prompt = f"""You are tasked with converting QA testing data into structured Playwright steps in JSON format.

**Context:**
The provided JSON contains the history of actions performed by a QA testing agent on a web application. Your job is to convert these actions into structured Playwright steps that can be programmatically compiled into a JavaScript test script.

**JSON Data:**
```json
{json_str}
```

**REQUIREMENTS:**

1. **OUTPUT FORMAT**: Return ONLY a JSON object with this structure:
```json
{{
  "steps": [
    {{
      "step_name": "",
      "playwright_code": "",
      "log_message": ""
    }},
    {{
      "step_name": "",
      "playwright_code": "",
      "log_message": ""
    }}
  ]
}}
```

2. **PLAYWRIGHT CODE REQUIREMENTS**:
   - Use ONLY CSS selectors, never XPath
   - Use modern Playwright syntax
   - Include appropriate timeouts (3-5 seconds max)
   - Use proper await syntax
   - Handle popups with try/catch blocks where needed

3. **SELECTOR STRATEGY**:
   - Prefer semantic selectors (button[type="submit"], input[type="email"])
   - Use attribute selectors over complex CSS paths
   - Avoid nth-child selectors when possible
   - Use text-based selectors for buttons/links when appropriate

**IMPORTANT NOTES:**
- Focus ONLY on converting the actions from the JSON into structured steps
- Each step should have clean, executable Playwright code
- Use descriptive step names and log messages
- Keep the code simple and focused on the specific action

**OUTPUT INSTRUCTIONS:**
Return ONLY the JSON object with the steps array. Do not include any explanations or markdown formatting around the JSON."""
    return prompt


async def _save_to_drcode_api(webui_manager: WebuiManager, summary: str, history: AgentHistoryList, openai_data: dict, summary_token_usage: dict, session_start_time: float):
    """Save QA report to drCode backend API."""
    try:

        # Get file paths
        task_id = getattr(webui_manager, 'bu_agent_task_id', 'unknown')
        save_agent_history_path = "./tmp/agent_history"

        # Get JSON file path
        json_file_path = os.path.join(save_agent_history_path, task_id, f"{task_id}.json")

        # Get GIF file path
        gif_file_path = os.path.join(save_agent_history_path, task_id, f"{task_id}.gif")

        # Get user prompt
        user_prompt = _get_user_prompt_from_history(webui_manager)

        # Read and encode files
        json_content = None
        gif_content = None
        session_data = None

        if os.path.exists(json_file_path):
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_content = f.read()
                # Also parse for Playwright generation
                session_data = json.loads(json_content)

        if os.path.exists(gif_file_path):
            with open(gif_file_path, 'rb') as f:
                gif_content = base64.b64encode(f.read()).decode('utf-8')

        # Generate Playwright steps JSON
        playwright_steps_json = None
        playwright_token_usage = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}

        if session_data:
            playwright_steps_json, playwright_token_usage = await _generate_playwright_steps_json(session_data)

        # Calculate total session time
        session_duration = time.time() - session_start_time

        # Calculate total tokens used across all steps
        browser_use_tokens = history.total_input_tokens()
        summary_tokens = summary_token_usage.get("total_tokens", 0)
        playwright_tokens = playwright_token_usage.get("total_tokens", 0)
        total_tokens = browser_use_tokens + summary_tokens + playwright_tokens

        # Prepare data for drCode API
        report_data = {
            "task_id": task_id,
            "user_prompt": user_prompt,
            "summary": summary,
            "json_data": json_content,
            "gif_data": gif_content,
            "playwright_steps_json": json.dumps(playwright_steps_json) if playwright_steps_json else None,
            "duration": history.total_duration_seconds(),
            "session_duration": session_duration,
            "tokens": total_tokens,
            "token_breakdown": {
                "browser_use_tokens": browser_use_tokens,
                "summary_generation_tokens": summary_tokens,
                "playwright_generation_tokens": playwright_tokens,
                "total_tokens": total_tokens,
                "summary_token_details": summary_token_usage,
                "playwright_token_details": playwright_token_usage
            },
            "status": "completed_with_errors" if history.errors() and any(history.errors()) else "completed",
            "errors": history.errors() if history.errors() and any(history.errors()) else None,
            "urls_visited": openai_data.get("urls", []),
            "timestamp": str(uuid.uuid4()),  # Using UUID as timestamp placeholder
            "user_id": None,
            "project_id": None
        }        
        
        # Get drCode API endpoint from environment
        drcode_api_url = os.getenv("DRCODE_API_URL", "https://devapi.drcode.ai/testgpt/api/uiTesting")

        if not drcode_api_url:
            logger.warning("DRCODE_API_URL not found, skipping drCode API save")
            return
        
        headers = {
            "Content-Type": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(drcode_api_url, json=report_data, headers=headers) as response:
                if response.status == 200:
                    logger.info("Successfully saved QA report to drCode API")
                else:
                    logger.error(f"Failed to save to drCode API: {response.status} - {await response.text()}")

    except Exception as e:
        logger.error(f"Error saving to drCode API: {e}", exc_info=True)


async def _ask_assistant_callback(
        webui_manager: WebuiManager, query: str, browser_context: BrowserContext
) -> Dict[str, Any]:
    """Callback triggered by the agent's ask_for_assistant action."""
    logger.info("Agent requires assistance. Waiting for user input.")

    if not hasattr(webui_manager, "_chat_history"):
        logger.error("Chat history not found in webui_manager during ask_assistant!")
        return {"response": "Internal Error: Cannot display help request."}

    webui_manager.bu_chat_history.append(
        {
            "role": "assistant",
            "content": f"**Need Help:** {query}\nPlease provide information or perform the required action in the browser, then type your response/confirmation below and click 'Submit Response'.",
        }
    )

    # Use state stored in webui_manager
    webui_manager.bu_response_event = asyncio.Event()
    webui_manager.bu_user_help_response = None  # Reset previous response

    try:
        logger.info("Waiting for user response event...")
        await asyncio.wait_for(
            webui_manager.bu_response_event.wait(), timeout=3600.0
        )  # Long timeout
        logger.info("User response event received.")
    except asyncio.TimeoutError:
        logger.warning("Timeout waiting for user assistance.")
        webui_manager.bu_chat_history.append(
            {
                "role": "assistant",
                "content": "**Timeout:** No response received. Trying to proceed.",
            }
        )
        webui_manager.bu_response_event = None  # Clear the event
        return {"response": "Timeout: User did not respond."}  # Inform the agent

    response = webui_manager.bu_user_help_response
    webui_manager.bu_chat_history.append(
        {"role": "user", "content": response}
    )  # Show user response in chat
    webui_manager.bu_response_event = (
        None  # Clear the event for the next potential request
    )
    return {"response": response}


# --- Core Agent Execution Logic --- (Needs access to webui_manager)


async def run_agent_task(
        webui_manager: WebuiManager, components: Dict[gr.components.Component, Any]
) -> AsyncGenerator[Dict[gr.components.Component, Any], None]:
    """Handles the entire lifecycle of initializing and running the agent."""

    # --- Get Components ---
    # Need handles to specific UI components to update them
    user_input_comp = webui_manager.get_component_by_id("browser_use_agent.user_input")
    run_button_comp = webui_manager.get_component_by_id("browser_use_agent.run_button")
    stop_button_comp = webui_manager.get_component_by_id(
        "browser_use_agent.stop_button"
    )
    pause_resume_button_comp = webui_manager.get_component_by_id(
        "browser_use_agent.pause_resume_button"
    )
    clear_button_comp = webui_manager.get_component_by_id(
        "browser_use_agent.clear_button"
    )
    chatbot_comp = webui_manager.get_component_by_id("browser_use_agent.chatbot")
    history_file_comp = webui_manager.get_component_by_id(
        "browser_use_agent.agent_history_file"
    )
    gif_comp = webui_manager.get_component_by_id("browser_use_agent.recording_gif")
    browser_view_comp = webui_manager.get_component_by_id(
        "browser_use_agent.browser_view"
    )

    # --- 1. Get Task and Initial UI Update ---
    task = components.get(user_input_comp, "").strip()
    if not task:
        gr.Warning("Please enter a task.")
        yield {run_button_comp: gr.update(interactive=True)}
        return

    # Set running state indirectly via _current_task
    webui_manager.bu_chat_history.append({"role": "user", "content": task})

    yield {
        user_input_comp: gr.Textbox(
            value="", interactive=False, placeholder="Agent is running..."
        ),
        run_button_comp: gr.Button(value="⏳ Running...", interactive=False),
        stop_button_comp: gr.Button(interactive=True),
        pause_resume_button_comp: gr.Button(value="⏸️ Pause", interactive=True),
        clear_button_comp: gr.Button(interactive=False),
        chatbot_comp: gr.update(value=webui_manager.bu_chat_history),
        history_file_comp: gr.update(value=None),
        gif_comp: gr.update(value=None),
    }

    # --- Agent Settings ---
    # Access settings values via components dict, getting IDs from webui_manager
    def get_setting(key, default=None):
        comp = webui_manager.id_to_component.get(f"agent_settings.{key}")
        return components.get(comp, default) if comp else default

    # CENTRALIZED SYSTEM PROMPT MANAGEMENT
    # Get system prompts from UI settings first
    override_system_prompt = get_setting("override_system_prompt") or None
    ui_extend_system_prompt = get_setting("extend_system_prompt") or None

    # Use centralized QA system prompt manager
    qa_system_prompt = get_qa_system_prompt()

    # Final system prompt logic:
    # 1. If user has override prompt in UI, use that (complete override)
    # 2. If user has extend prompt in UI, use that (ignore QA)
    # 3. Otherwise, use centralized QA prompt
    if override_system_prompt:
        extend_system_prompt = None
        logger.info("Using user-provided override system prompt from UI")
    elif ui_extend_system_prompt:
        extend_system_prompt = ui_extend_system_prompt
        logger.info("Using user-provided extend system prompt from UI")
    else:
        extend_system_prompt = qa_system_prompt
        if qa_system_prompt:
            logger.info("Using centralized QA system prompt")
    llm_provider_name = get_setting(
        "llm_provider", None
    )  # Default to None if not found
    llm_model_name = get_setting("llm_model_name", None)
    llm_temperature = get_setting("llm_temperature", 0.6)
    use_vision = get_setting("use_vision", True)
    ollama_num_ctx = get_setting("ollama_num_ctx", 16000)
    llm_base_url = get_setting("llm_base_url") or None
    llm_api_key = get_setting("llm_api_key") or None
    max_steps = get_setting("max_steps", 100)
    max_actions = get_setting("max_actions", 10)
    max_input_tokens = get_setting("max_input_tokens", 128000)
    tool_calling_str = get_setting("tool_calling_method", "auto")
    tool_calling_method = tool_calling_str if tool_calling_str != "None" else None
    mcp_server_config_comp = webui_manager.id_to_component.get(
        "agent_settings.mcp_server_config"
    )
    mcp_server_config_str = (
        components.get(mcp_server_config_comp) if mcp_server_config_comp else None
    )
    mcp_server_config = (
        json.loads(mcp_server_config_str) if mcp_server_config_str else None
    )

    # Planner LLM Settings (Optional)
    planner_llm_provider_name = get_setting("planner_llm_provider") or None
    planner_llm = None
    planner_use_vision = False
    if planner_llm_provider_name:
        planner_llm_model_name = get_setting("planner_llm_model_name")
        planner_llm_temperature = get_setting("planner_llm_temperature", 0.6)
        planner_ollama_num_ctx = get_setting("planner_ollama_num_ctx", 16000)
        planner_llm_base_url = get_setting("planner_llm_base_url") or None
        planner_llm_api_key = get_setting("planner_llm_api_key") or None
        planner_use_vision = get_setting("planner_use_vision", False)

        planner_llm = await _initialize_llm(
            planner_llm_provider_name,
            planner_llm_model_name,
            planner_llm_temperature,
            planner_llm_base_url,
            planner_llm_api_key,
            planner_ollama_num_ctx if planner_llm_provider_name == "ollama" else None,
        )

    # --- Browser Settings ---
    def get_browser_setting(key, default=None):
        comp = webui_manager.id_to_component.get(f"browser_settings.{key}")
        return components.get(comp, default) if comp else default

    browser_binary_path = get_browser_setting("browser_binary_path") or None
    browser_user_data_dir = get_browser_setting("browser_user_data_dir") or None
    use_own_browser = get_browser_setting(
        "use_own_browser", False
    )  # Logic handled by CDP/WSS presence
    keep_browser_open = get_browser_setting("keep_browser_open", False)
    headless = get_browser_setting("headless", False)
    disable_security = get_browser_setting("disable_security", False)
    window_w = int(get_browser_setting("window_w", 1280))
    window_h = int(get_browser_setting("window_h", 1100))
    cdp_url = get_browser_setting("cdp_url") or None
    wss_url = get_browser_setting("wss_url") or None
    save_recording_path = get_browser_setting("save_recording_path") or None
    save_trace_path = get_browser_setting("save_trace_path") or None
    save_agent_history_path = get_browser_setting(
        "save_agent_history_path", "./tmp/agent_history"
    )
    save_download_path = get_browser_setting("save_download_path", "./tmp/downloads")

    stream_vw = 70
    stream_vh = int(70 * window_h // window_w)

    os.makedirs(save_agent_history_path, exist_ok=True)
    if save_recording_path:
        os.makedirs(save_recording_path, exist_ok=True)
    if save_trace_path:
        os.makedirs(save_trace_path, exist_ok=True)
    if save_download_path:
        os.makedirs(save_download_path, exist_ok=True)

    # --- 2. Initialize LLM ---
    main_llm = await _initialize_llm(
        llm_provider_name,
        llm_model_name,
        llm_temperature,
        llm_base_url,
        llm_api_key,
        ollama_num_ctx if llm_provider_name == "ollama" else None,
    )

    # Pass the webui_manager instance to the callback when wrapping it
    async def ask_callback_wrapper(
            query: str, browser_context: BrowserContext
    ) -> Dict[str, Any]:
        return await _ask_assistant_callback(webui_manager, query, browser_context)

    if not webui_manager.bu_controller:
        webui_manager.bu_controller = CustomController(
            ask_assistant_callback=ask_callback_wrapper
        )
        await webui_manager.bu_controller.setup_mcp_client(mcp_server_config)

    # --- 4. Initialize Browser and Context ---
    should_close_browser_on_finish = not keep_browser_open

    try:
        # Close existing resources if not keeping open
        if not keep_browser_open:
            if webui_manager.bu_browser_context:
                logger.info("Closing previous browser context.")
                await webui_manager.bu_browser_context.close()
                webui_manager.bu_browser_context = None
            if webui_manager.bu_browser:
                logger.info("Closing previous browser.")
                await webui_manager.bu_browser.close()
                webui_manager.bu_browser = None

        # Create Browser if needed
        if not webui_manager.bu_browser:
            logger.info("Launching new browser instance.")
            extra_args = []
            if use_own_browser:
                browser_binary_path = os.getenv("BROWSER_PATH", None) or browser_binary_path
                if browser_binary_path == "":
                    browser_binary_path = None
                browser_user_data = browser_user_data_dir or os.getenv("BROWSER_USER_DATA", None)
                if browser_user_data:
                    extra_args += [f"--user-data-dir={browser_user_data}"]
            else:
                browser_binary_path = None

            webui_manager.bu_browser = CustomBrowser(
                config=BrowserConfig(
                    headless=headless,
                    disable_security=disable_security,
                    browser_binary_path=browser_binary_path,
                    extra_browser_args=extra_args,
                    wss_url=wss_url,
                    cdp_url=cdp_url,
                    new_context_config=BrowserContextConfig(
                        window_width=window_w,
                        window_height=window_h,
                    )
                )
            )

        # Always create a fresh context for each session to ensure clean state
        if webui_manager.bu_browser_context:
            logger.info("Closing existing browser context to create fresh state.")
            await webui_manager.bu_browser_context.close()
            webui_manager.bu_browser_context = None

        # Create fresh Context
        if not webui_manager.bu_browser_context:
            logger.info("Creating new browser context.")
            context_config = BrowserContextConfig(
                trace_path=save_trace_path if save_trace_path else None,
                save_recording_path=save_recording_path
                if save_recording_path
                else None,
                save_downloads_path=save_download_path if save_download_path else None,
                window_height=window_h,
                window_width=window_w,
            )
            if not webui_manager.bu_browser:
                raise ValueError("Browser not initialized, cannot create context.")
            webui_manager.bu_browser_context = (
                await webui_manager.bu_browser.new_context(config=context_config)
            )

        # --- 5. Initialize or Update Agent ---
        webui_manager.bu_agent_task_id = str(uuid.uuid4())  # New ID for this task run
        os.makedirs(
            os.path.join(save_agent_history_path, webui_manager.bu_agent_task_id),
            exist_ok=True,
        )
        history_file = os.path.join(
            save_agent_history_path,
            webui_manager.bu_agent_task_id,
            f"{webui_manager.bu_agent_task_id}.json",
        )
        gif_path = os.path.join(
            save_agent_history_path,
            webui_manager.bu_agent_task_id,
            f"{webui_manager.bu_agent_task_id}.gif",
        )

        # Pass the webui_manager to callbacks when wrapping them
        async def step_callback_wrapper(
                state: BrowserState, output: AgentOutput, step_num: int
        ):
            await _handle_new_step(webui_manager, state, output, step_num)

        def done_callback_wrapper(history: AgentHistoryList):
            _handle_done(webui_manager, history)

        if not webui_manager.bu_agent:
            logger.info(f"Initializing new agent for task: {task}")
            if not webui_manager.bu_browser or not webui_manager.bu_browser_context:
                raise ValueError(
                    "Browser or Context not initialized, cannot create agent."
                )
            webui_manager.bu_agent = BrowserUseAgent(
                task=task,
                llm=main_llm,
                browser=webui_manager.bu_browser,
                browser_context=webui_manager.bu_browser_context,
                controller=webui_manager.bu_controller,
                register_new_step_callback=step_callback_wrapper,
                register_done_callback=done_callback_wrapper,
                use_vision=use_vision,
                override_system_message=override_system_prompt,
                extend_system_message=extend_system_prompt,
                max_input_tokens=max_input_tokens,
                max_actions_per_step=max_actions,
                tool_calling_method=tool_calling_method,
                planner_llm=planner_llm,
                use_vision_for_planner=planner_use_vision if planner_llm else False,
                source="webui",
            )
            webui_manager.bu_agent.state.agent_id = webui_manager.bu_agent_task_id
            webui_manager.bu_agent.settings.generate_gif = gif_path
        else:
            webui_manager.bu_agent.state.agent_id = webui_manager.bu_agent_task_id
            webui_manager.bu_agent.add_new_task(task)
            webui_manager.bu_agent.settings.generate_gif = gif_path
            webui_manager.bu_agent.browser = webui_manager.bu_browser
            webui_manager.bu_agent.browser_context = webui_manager.bu_browser_context
            webui_manager.bu_agent.controller = webui_manager.bu_controller

        # --- 6. Run Agent Task and Stream Updates ---
        # Track session start time for duration calculation
        webui_manager.bu_session_start_time = time.time()

        agent_run_coro = webui_manager.bu_agent.run(max_steps=max_steps)
        agent_task = asyncio.create_task(agent_run_coro)
        webui_manager.bu_current_task = agent_task  # Store the task

        last_chat_len = len(webui_manager.bu_chat_history)
        while not agent_task.done():
            is_paused = webui_manager.bu_agent.state.paused
            is_stopped = webui_manager.bu_agent.state.stopped

            # Check for pause state
            if is_paused:
                yield {
                    pause_resume_button_comp: gr.update(
                        value="▶️ Resume", interactive=True
                    ),
                    stop_button_comp: gr.update(interactive=True),
                }
                # Wait until pause is released or task is stopped/done
                while is_paused and not agent_task.done():
                    # Re-check agent state in loop
                    is_paused = webui_manager.bu_agent.state.paused
                    is_stopped = webui_manager.bu_agent.state.stopped
                    if is_stopped:  # Stop signal received while paused
                        break
                    await asyncio.sleep(0.2)

                if (
                        agent_task.done() or is_stopped
                ):  # If stopped or task finished while paused
                    break

                # If resumed, yield UI update
                yield {
                    pause_resume_button_comp: gr.update(
                        value="⏸️ Pause", interactive=True
                    ),
                    run_button_comp: gr.update(
                        value="⏳ Running...", interactive=False
                    ),
                }

            # Check if agent stopped itself or stop button was pressed (which sets agent.state.stopped)
            if is_stopped:
                logger.info("Agent has stopped (internally or via stop button).")
                if not agent_task.done():
                    # Ensure the task coroutine finishes if agent just set flag
                    try:
                        await asyncio.wait_for(
                            agent_task, timeout=1.0
                        )  # Give it a moment to exit run()
                    except asyncio.TimeoutError:
                        logger.warning(
                            "Agent task did not finish quickly after stop signal, cancelling."
                        )
                        agent_task.cancel()
                    except Exception:  # Catch task exceptions if it errors on stop
                        pass
                break  # Exit the streaming loop

            # Check if agent is asking for help (via response_event)
            update_dict = {}
            if webui_manager.bu_response_event is not None:
                update_dict = {
                    user_input_comp: gr.update(
                        placeholder="Agent needs help. Enter response and submit.",
                        interactive=True,
                    ),
                    run_button_comp: gr.update(
                        value="✔️ Submit Response", interactive=True
                    ),
                    pause_resume_button_comp: gr.update(interactive=False),
                    stop_button_comp: gr.update(interactive=False),
                    chatbot_comp: gr.update(value=webui_manager.bu_chat_history),
                }
                last_chat_len = len(webui_manager.bu_chat_history)
                yield update_dict
                # Wait until response is submitted or task finishes
                while (
                        webui_manager.bu_response_event is not None
                        and not agent_task.done()
                ):
                    await asyncio.sleep(0.2)
                # Restore UI after response submitted or if task ended unexpectedly
                if not agent_task.done():
                    yield {
                        user_input_comp: gr.update(
                            placeholder="Agent is running...", interactive=False
                        ),
                        run_button_comp: gr.update(
                            value="⏳ Running...", interactive=False
                        ),
                        pause_resume_button_comp: gr.update(interactive=True),
                        stop_button_comp: gr.update(interactive=True),
                    }
                else:
                    break  # Task finished while waiting for response

            # Update Chatbot if new messages arrived via callbacks
            if len(webui_manager.bu_chat_history) > last_chat_len:
                update_dict[chatbot_comp] = gr.update(
                    value=webui_manager.bu_chat_history
                )
                last_chat_len = len(webui_manager.bu_chat_history)

            # Update Browser View
            if headless and webui_manager.bu_browser_context:
                try:
                    screenshot_b64 = (
                        await webui_manager.bu_browser_context.take_screenshot()
                    )
                    if screenshot_b64:
                        html_content = f'<img src="data:image/jpeg;base64,{screenshot_b64}" style="width:{stream_vw}vw; height:{stream_vh}vh ; border:1px solid #ccc;">'
                        update_dict[browser_view_comp] = gr.update(
                            value=html_content, visible=True
                        )
                    else:
                        html_content = f"<h1 style='width:{stream_vw}vw; height:{stream_vh}vh'>Waiting for browser session...</h1>"
                        update_dict[browser_view_comp] = gr.update(
                            value=html_content, visible=True
                        )
                except Exception as e:
                    logger.debug(f"Failed to capture screenshot: {e}")
                    update_dict[browser_view_comp] = gr.update(
                        value="<div style='...'>Error loading view...</div>",
                        visible=True,
                    )
            else:
                update_dict[browser_view_comp] = gr.update(visible=False)

            # Yield accumulated updates
            if update_dict:
                yield update_dict

            await asyncio.sleep(0.1)  # Polling interval

        # --- 7. Task Finalization ---
        webui_manager.bu_agent.state.paused = False
        webui_manager.bu_agent.state.stopped = False
        final_update = {}
        try:
            logger.info("Agent task completing...")
            # Await the task ensure completion and catch exceptions if not already caught
            if not agent_task.done():
                await agent_task  # Retrieve result/exception
            elif agent_task.exception():  # Check if task finished with exception
                agent_task.result()  # Raise the exception to be caught below
            logger.info("Agent task completed processing.")

            logger.info(f"Explicitly saving agent history to: {history_file}")
            webui_manager.bu_agent.save_history(history_file)

            if os.path.exists(history_file):
                final_update[history_file_comp] = gr.File(value=history_file)

            if gif_path and os.path.exists(gif_path):
                logger.info(f"GIF found at: {gif_path}")
                final_update[gif_comp] = gr.Image(value=gif_path)

        except asyncio.CancelledError:
            logger.info("Agent task was cancelled.")
            if not any(
                    "Cancelled" in msg.get("content", "")
                    for msg in webui_manager.bu_chat_history
                    if msg.get("role") == "assistant"
            ):
                webui_manager.bu_chat_history.append(
                    {"role": "assistant", "content": "**Task Cancelled**."}
                )
            final_update[chatbot_comp] = gr.update(value=webui_manager.bu_chat_history)
        except Exception as e:
            logger.error(f"Error during agent execution: {e}", exc_info=True)
            error_message = (
                f"**Agent Execution Error:**\n```\n{type(e).__name__}: {e}\n```"
            )
            if not any(
                    error_message in msg.get("content", "")
                    for msg in webui_manager.bu_chat_history
                    if msg.get("role") == "assistant"
            ):
                webui_manager.bu_chat_history.append(
                    {"role": "assistant", "content": error_message}
                )
            final_update[chatbot_comp] = gr.update(value=webui_manager.bu_chat_history)
            gr.Error(f"Agent execution failed: {e}")

        finally:
            webui_manager.bu_current_task = None  # Clear the task reference

            # Close browser/context if requested
            if should_close_browser_on_finish:
                if webui_manager.bu_browser_context:
                    logger.info("Closing browser context after task.")
                    await webui_manager.bu_browser_context.close()
                    webui_manager.bu_browser_context = None
                if webui_manager.bu_browser:
                    logger.info("Closing browser after task.")
                    await webui_manager.bu_browser.close()
                    webui_manager.bu_browser = None

            # --- 8. Final UI Update ---
            final_update.update(
                {
                    user_input_comp: gr.update(
                        value="",
                        interactive=True,
                        placeholder="Enter your next task...",
                    ),
                    run_button_comp: gr.update(value="▶️ Submit Task", interactive=True),
                    stop_button_comp: gr.update(value="⏹️ Stop", interactive=False),
                    pause_resume_button_comp: gr.update(
                        value="⏸️ Pause", interactive=False
                    ),
                    clear_button_comp: gr.update(interactive=True),
                    # Ensure final chat history is shown
                    chatbot_comp: gr.update(value=webui_manager.bu_chat_history),
                }
            )
            yield final_update

            # --- 9. Handle QA Summary Processing ---
            if hasattr(webui_manager, 'bu_summary_processing_needed') and webui_manager.bu_summary_processing_needed:
                logger.info("Starting QA summary processing...")
                webui_manager.bu_summary_processing_needed = False

                # Process summary in background and continue yielding updates
                summary_task = asyncio.create_task(_process_qa_summary_async(webui_manager, webui_manager.bu_summary_history))

                # Continue yielding updates until summary is ready
                while not summary_task.done():
                    await asyncio.sleep(0.5)  # Check every 500ms

                    # Yield current chat history in case it was updated
                    yield {chatbot_comp: gr.update(value=webui_manager.bu_chat_history)}

                # Final update after summary is complete
                try:
                    await summary_task  # Ensure any exceptions are raised
                    yield {chatbot_comp: gr.update(value=webui_manager.bu_chat_history)}
                    logger.info("QA summary processing completed and UI updated")
                except Exception as summary_error:
                    logger.error(f"Error in summary processing: {summary_error}", exc_info=True)

    except Exception as e:
        # Catch errors during setup (before agent run starts)
        logger.error(f"Error setting up agent task: {e}", exc_info=True)
        webui_manager.bu_current_task = None  # Ensure state is reset
        yield {
            user_input_comp: gr.update(
                interactive=True, placeholder="Error during setup. Enter task..."
            ),
            run_button_comp: gr.update(value="▶️ Submit Task", interactive=True),
            stop_button_comp: gr.update(value="⏹️ Stop", interactive=False),
            pause_resume_button_comp: gr.update(value="⏸️ Pause", interactive=False),
            clear_button_comp: gr.update(interactive=True),
            chatbot_comp: gr.update(
                value=webui_manager.bu_chat_history
                      + [{"role": "assistant", "content": f"**Setup Error:** {e}"}]
            ),
        }


# --- Button Click Handlers --- (Need access to webui_manager)


async def handle_submit(
        webui_manager: WebuiManager, components: Dict[gr.components.Component, Any]
):
    """Handles clicks on the main 'Submit' button."""
    user_input_comp = webui_manager.get_component_by_id("browser_use_agent.user_input")
    user_input_value = components.get(user_input_comp, "").strip()

    # Check if waiting for user assistance
    if webui_manager.bu_response_event and not webui_manager.bu_response_event.is_set():
        logger.info(f"User submitted assistance: {user_input_value}")
        webui_manager.bu_user_help_response = (
            user_input_value if user_input_value else "User provided no text response."
        )
        webui_manager.bu_response_event.set()
        # UI updates handled by the main loop reacting to the event being set
        yield {
            user_input_comp: gr.update(
                value="",
                interactive=False,
                placeholder="Waiting for agent to continue...",
            ),
            webui_manager.get_component_by_id(
                "browser_use_agent.run_button"
            ): gr.update(value="⏳ Running...", interactive=False),
        }
    # Check if a task is currently running (using _current_task)
    elif webui_manager.bu_current_task and not webui_manager.bu_current_task.done():
        logger.warning(
            "Submit button clicked while agent is already running and not asking for help."
        )
        gr.Info("Agent is currently running. Please wait or use Stop/Pause.")
        yield {}  # No change
    else:
        # Handle submission for a new task
        logger.info("Submit button clicked for new task.")
        # Use async generator to stream updates from run_agent_task
        async for update in run_agent_task(webui_manager, components):
            yield update


async def handle_stop(webui_manager: WebuiManager):
    """Handles clicks on the 'Stop' button."""
    logger.info("Stop button clicked.")
    agent = webui_manager.bu_agent
    task = webui_manager.bu_current_task

    if agent and task and not task.done():
        # Signal the agent to stop by setting its internal flag
        agent.state.stopped = True
        agent.state.paused = False  # Ensure not paused if stopped
        return {
            webui_manager.get_component_by_id(
                "browser_use_agent.stop_button"
            ): gr.update(interactive=False, value="⏹️ Stopping..."),
            webui_manager.get_component_by_id(
                "browser_use_agent.pause_resume_button"
            ): gr.update(interactive=False),
            webui_manager.get_component_by_id(
                "browser_use_agent.run_button"
            ): gr.update(interactive=False),
        }
    else:
        logger.warning("Stop clicked but agent is not running or task is already done.")
        # Reset UI just in case it's stuck
        return {
            webui_manager.get_component_by_id(
                "browser_use_agent.run_button"
            ): gr.update(interactive=True),
            webui_manager.get_component_by_id(
                "browser_use_agent.stop_button"
            ): gr.update(interactive=False),
            webui_manager.get_component_by_id(
                "browser_use_agent.pause_resume_button"
            ): gr.update(interactive=False),
            webui_manager.get_component_by_id(
                "browser_use_agent.clear_button"
            ): gr.update(interactive=True),
        }


async def handle_pause_resume(webui_manager: WebuiManager):
    """Handles clicks on the 'Pause/Resume' button."""
    agent = webui_manager.bu_agent
    task = webui_manager.bu_current_task

    if agent and task and not task.done():
        if agent.state.paused:
            logger.info("Resume button clicked.")
            agent.resume()
            # UI update happens in main loop
            return {
                webui_manager.get_component_by_id(
                    "browser_use_agent.pause_resume_button"
                ): gr.update(value="⏸️ Pause", interactive=True)
            }  # Optimistic update
        else:
            logger.info("Pause button clicked.")
            agent.pause()
            return {
                webui_manager.get_component_by_id(
                    "browser_use_agent.pause_resume_button"
                ): gr.update(value="▶️ Resume", interactive=True)
            }  # Optimistic update
    else:
        logger.warning(
            "Pause/Resume clicked but agent is not running or doesn't support state."
        )
        return {}  # No change


async def handle_clear(webui_manager: WebuiManager):
    """Handles clicks on the 'Clear' button."""
    logger.info("Clear button clicked.")

    # Stop any running task first
    task = webui_manager.bu_current_task
    if task and not task.done():
        logger.info("Clearing requires stopping the current task.")
        webui_manager.bu_agent.stop()
        task.cancel()
        try:
            await asyncio.wait_for(task, timeout=2.0)  # Wait briefly
        except (asyncio.CancelledError, asyncio.TimeoutError):
            pass
        except Exception as e:
            logger.warning(f"Error stopping task on clear: {e}")
    webui_manager.bu_current_task = None

    if webui_manager.bu_controller:
        await webui_manager.bu_controller.close_mcp_client()
        webui_manager.bu_controller = None
    webui_manager.bu_agent = None

    # Reset state stored in manager
    webui_manager.bu_chat_history = []
    webui_manager.bu_response_event = None
    webui_manager.bu_user_help_response = None
    webui_manager.bu_agent_task_id = None

    logger.info("Agent state and browser resources cleared.")

    # Reset UI components
    return {
        webui_manager.get_component_by_id("browser_use_agent.chatbot"): gr.update(
            value=[]
        ),
        webui_manager.get_component_by_id("browser_use_agent.user_input"): gr.update(
            value="", placeholder="Enter your task here..."
        ),
        webui_manager.get_component_by_id(
            "browser_use_agent.agent_history_file"
        ): gr.update(value=None),
        webui_manager.get_component_by_id("browser_use_agent.recording_gif"): gr.update(
            value=None
        ),
        webui_manager.get_component_by_id("browser_use_agent.browser_view"): gr.update(
            value="<div style='...'>Browser Cleared</div>"
        ),
        webui_manager.get_component_by_id("browser_use_agent.run_button"): gr.update(
            value="▶️ Submit Task", interactive=True
        ),
        webui_manager.get_component_by_id("browser_use_agent.stop_button"): gr.update(
            interactive=False
        ),
        webui_manager.get_component_by_id(
            "browser_use_agent.pause_resume_button"
        ): gr.update(value="⏸️ Pause", interactive=False),
        webui_manager.get_component_by_id("browser_use_agent.clear_button"): gr.update(
            interactive=True
        ),
    }


# --- Tab Creation Function ---


def create_browser_use_agent_tab(webui_manager: WebuiManager):
    """
    Create the run agent tab, defining UI, state, and handlers.
    """
    webui_manager.init_browser_use_agent()

    # --- Define UI Components ---
    tab_components = {}
    with gr.Column():
        chatbot = gr.Chatbot(
            lambda: webui_manager.bu_chat_history,  # Load history dynamically
            elem_id="browser_use_chatbot",
            label="Agent Interaction",
            type="messages",
            height=600,
            show_copy_button=True,
        )
        user_input = gr.Textbox(
            label="Your Task or Response",
            placeholder="Enter your task here or provide assistance when asked.",
            lines=3,
            interactive=True,
            elem_id="user_input",
        )
        with gr.Row():
            stop_button = gr.Button(
                "⏹️ Stop", interactive=False, variant="stop", scale=2
            )
            pause_resume_button = gr.Button(
                "⏸️ Pause", interactive=False, variant="secondary", scale=2, visible=True
            )
            clear_button = gr.Button(
                "🗑️ Clear", interactive=True, variant="secondary", scale=2
            )
            run_button = gr.Button("▶️ Submit Task", variant="primary", scale=3)

        browser_view = gr.HTML(
            value="<div style='width:100%; height:50vh; display:flex; justify-content:center; align-items:center; border:1px solid #ccc; background-color:#f0f0f0;'><p>Browser View (Requires Headless=True)</p></div>",
            label="Browser Live View",
            elem_id="browser_view",
            visible=False,
        )
        with gr.Column():
            gr.Markdown("### Task Outputs")
            agent_history_file = gr.File(label="Agent History JSON", interactive=False, visible=False)
            recording_gif = gr.Image(
                label="Task Recording GIF",
                format="gif",
                interactive=False,
                type="filepath",
            )

    # --- Store Components in Manager ---
    tab_components.update(
        dict(
            chatbot=chatbot,
            user_input=user_input,
            clear_button=clear_button,
            run_button=run_button,
            stop_button=stop_button,
            pause_resume_button=pause_resume_button,
            agent_history_file=agent_history_file,
            recording_gif=recording_gif,
            browser_view=browser_view,
        )
    )
    webui_manager.add_components(
        "browser_use_agent", tab_components
    )  # Use "browser_use_agent" as tab_name prefix

    all_managed_components = set(
        webui_manager.get_components()
    )  # Get all components known to manager
    run_tab_outputs = list(tab_components.values())

    async def submit_wrapper(
            components_dict: Dict[Component, Any],
    ) -> AsyncGenerator[Dict[Component, Any], None]:
        """Wrapper for handle_submit that yields its results."""
        async for update in handle_submit(webui_manager, components_dict):
            yield update

    async def stop_wrapper() -> AsyncGenerator[Dict[Component, Any], None]:
        """Wrapper for handle_stop."""
        update_dict = await handle_stop(webui_manager)
        yield update_dict

    async def pause_resume_wrapper() -> AsyncGenerator[Dict[Component, Any], None]:
        """Wrapper for handle_pause_resume."""
        update_dict = await handle_pause_resume(webui_manager)
        yield update_dict

    async def clear_wrapper() -> AsyncGenerator[Dict[Component, Any], None]:
        """Wrapper for handle_clear."""
        update_dict = await handle_clear(webui_manager)
        yield update_dict

    # --- Connect Event Handlers using the Wrappers --
    run_button.click(
        fn=submit_wrapper, inputs=all_managed_components, outputs=run_tab_outputs
    )
    user_input.submit(
        fn=submit_wrapper, inputs=all_managed_components, outputs=run_tab_outputs
    )
    stop_button.click(fn=stop_wrapper, inputs=None, outputs=run_tab_outputs)
    pause_resume_button.click(
        fn=pause_resume_wrapper, inputs=None, outputs=run_tab_outputs
    )
    clear_button.click(fn=clear_wrapper, inputs=None, outputs=run_tab_outputs)

    # print(webui_manager.bu_chat_history)

    # # Add a test message to the chat history
    # webui_manager.bu_chat_history.append({
    #     "role": "assistant",
    #     "content": {
    #         "current_state": {
    #             "evaluation_previous_goal": "Test",
    #             "memory": "Test memory",
    #             "next_goal": "Test next goal"
    #         },
    #         "action": [
    #             {"input_text": {"index": 1, "text": "<EMAIL>"}}
    #         ]
    #     }
    # })


# --- Playwright Template Code (for future use) ---
# This template will be used in the future for runtime API calls to generate Playwright scripts

def _get_playwright_template() -> str:
    """
    Get the Playwright JavaScript template with placeholders for steps.
    This template is included for future use but not currently utilized.

    Returns:
        Template string with placeholder for steps
    """
    template = '''const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const { chromium } = require('playwright');

// Helper function to validate step completion
async function validateStepCompletion(page, stepName, validationCode = null) {
    try {
        if (validationCode) {
            // Execute custom validation code with retry logic
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts) {
                try {
                    const result = await page.evaluate(validationCode);
                    if (result) {
                        console.log(`✅ Validation passed for: ${stepName}`);
                        return true;
                    }

                    // Wait a bit before retrying
                    if (attempts < maxAttempts - 1) {
                        await page.waitForTimeout(1000);
                        attempts++;
                        console.log(`🔄 Retrying validation for: ${stepName} (attempt ${attempts + 1}/${maxAttempts})`);
                    } else {
                        break;
                    }
                } catch (evalError) {
                    console.log(`⚠️  Validation evaluation error for ${stepName}:`, evalError.message);
                    if (attempts < maxAttempts - 1) {
                        await page.waitForTimeout(1000);
                        attempts++;
                    } else {
                        break;
                    }
                }
            }

            console.log(`❌ Validation failed for: ${stepName} after ${maxAttempts} attempts`);
            return false;
        } else {
            // Default validation - check if page is responsive
            await page.evaluate(() => document.readyState);
            console.log(`✅ Basic validation passed for: ${stepName}`);
            return true;
        }
    } catch (error) {
        console.log(`❌ Validation error for ${stepName}:`, error.message);
        return false;
    }
}

// Helper function to wait for page stability
async function waitForPageStability(page, timeout = 5000) {
    try {
        // First, wait for DOM content to be loaded
        await page.waitForLoadState('domcontentloaded', { timeout: timeout / 2 });

        // Then wait for network to be idle (shorter timeout)
        await page.waitForLoadState('networkidle', { timeout: timeout / 2 });

        // Wait for any loading indicators to disappear
        try {
            await page.waitForFunction(() => {
                const loadingElements = document.querySelectorAll('.loading, .spinner, [data-loading="true"], .loader');
                return loadingElements.length === 0;
            }, { timeout: 2000 });
        } catch (loadingTimeout) {
            // Continue if loading indicators don't disappear
        }

        // Additional wait for any dynamic content
        await page.waitForTimeout(1000);
        console.log('📄 Page stability confirmed');
    } catch (error) {
        console.log('⚠️  Page stability timeout, continuing...');
        // Even if stability times out, wait a minimum amount
        await page.waitForTimeout(500);
    }
}

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();

    try {
        console.log('🚀 Starting Playwright test execution...');

        // Wait for initial page load
        await waitForPageStability(page);

        // STEPS_PLACEHOLDER

        console.log('✅ Test completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();
    }
})();'''
    return template
